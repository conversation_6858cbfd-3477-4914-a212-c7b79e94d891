<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class News extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'excerpt',
        'image',
        'category',
        'is_featured',
        'is_published',
        'published_at'
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'is_published' => 'boolean',
        'published_at' => 'datetime'
    ];

    // Scope สำหรับข่าวที่เผยแพร่แล้ว
    public function scopePublished($query)
    {
        return $query->where('is_published', true)
                    ->where('published_at', '<=', now());
    }

    // Scope สำหรับข่าวเด่น
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    // Accessor สำหรับ excerpt
    public function getExcerptAttribute($value)
    {
        if ($value) {
            return $value;
        }

        return mb_substr(strip_tags($this->content), 0, 150) . '...';
    }

    // Accessor สำหรับวันที่แสดง
    public function getFormattedDateAttribute()
    {
        return $this->published_at ?
            $this->published_at->format('d/m/Y') :
            $this->created_at->format('d/m/Y');
    }

    // Accessor สำหรับรูปภาพที่จะแสดง
    public function getDisplayImageAttribute()
    {
        if ($this->image) {
            return asset($this->image);
        }

        return null;
    }
}
