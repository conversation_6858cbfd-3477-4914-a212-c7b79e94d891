<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;

class NewsController extends Controller
{
    public function index(Request $request)
    {
        $query = News::published()->orderBy('published_at', 'desc');

        // ค้นหา
        if ($request->has('search') && $request->search) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('content', 'like', '%' . $request->search . '%');
            });
        }

        // กรองตามหมวดหมู่
        if ($request->has('category') && $request->category) {
            $query->where('category', $request->category);
        }

        $news = $query->paginate(10);
        $categories = News::published()->distinct()->pluck('category');

        return view('news.index', compact('news', 'categories'));
    }

    public function show($id)
    {
        $news = News::published()->findOrFail($id);
        $relatedNews = News::published()
            ->where('id', '!=', $id)
            ->where('category', $news->category)
            ->limit(3)
            ->get();

        return view('news.show', compact('news', 'relatedNews'));
    }
}
