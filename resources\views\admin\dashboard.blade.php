@extends('layouts.app')

@section('title', 'แดชบอร์ด - Admin')

@section('content')
<!-- <PERSON> Header -->
<section class="py-4 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>แดชบอร์ด
                </h1>
                <p class="mb-0 opacity-75">ภาพรวมของระบบจัดการ</p>
            </div>
            <div class="col-auto">
                <div class="text-end">
                    <small class="opacity-75">เข้าสู่ระบบล่าสุด</small><br>
                    <span>{{ now()->format('d/m/Y H:i') }}</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Cards -->
<section class="py-4">
    <div class="container">
        <div class="row g-4 mb-4">
            <!-- Total News -->
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="display-4 text-primary mb-2">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <h3 class="h2 mb-1">{{ $stats['total_news'] }}</h3>
                        <p class="text-muted mb-0">ข่าวสารทั้งหมด</p>
                    </div>
                </div>
            </div>

            <!-- Published News -->
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="display-4 text-success mb-2">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3 class="h2 mb-1">{{ $stats['published_news'] }}</h3>
                        <p class="text-muted mb-0">ข่าวที่เผยแพร่</p>
                    </div>
                </div>
            </div>

            <!-- Draft News -->
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="display-4 text-warning mb-2">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h3 class="h2 mb-1">{{ $stats['draft_news'] }}</h3>
                        <p class="text-muted mb-0">ข่าวร่าง</p>
                    </div>
                </div>
            </div>

            <!-- Total Users -->
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="display-4 text-info mb-2">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="h2 mb-1">{{ $stats['total_users'] }}</h3>
                        <p class="text-muted mb-0">ผู้ใช้ทั้งหมด</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <!-- Recent News -->
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>ข่าวล่าสุด
                            </h5>
                            <a href="{{ route('admin.news.index') }}" class="btn btn-sm btn-outline-primary">
                                ดูทั้งหมด
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        @if($recent_news->count() > 0)
                            <div class="list-group list-group-flush">
                                @foreach($recent_news as $news)
                                <div class="list-group-item">
                                    <div class="d-flex align-items-start">
                                        @if($news->image)
                                        <img src="{{ asset($news->image) }}" 
                                             class="rounded me-3" 
                                             style="width: 60px; height: 60px; object-fit: cover;">
                                        @else
                                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                             style="width: 60px; height: 60px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                        @endif
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <a href="{{ route('admin.news.edit', $news->id) }}" 
                                                   class="text-decoration-none">
                                                    {{ $news->title }}
                                                </a>
                                            </h6>
                                            <p class="mb-1 text-muted small">
                                                {{ Str::limit(strip_tags($news->content), 100) }}
                                            </p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ $news->created_at->format('d/m/Y H:i') }}
                                                </small>
                                                <span class="badge {{ $news->is_published ? 'bg-success' : 'bg-warning' }}">
                                                    {{ $news->is_published ? 'เผยแพร่' : 'ร่าง' }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                                <p class="text-muted">ยังไม่มีข่าวสาร</p>
                                <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>เพิ่มข่าวใหม่
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>การดำเนินการด่วน
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เพิ่มข่าวใหม่
                            </a>
                            <a href="{{ route('admin.news.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-list me-2"></i>จัดการข่าวทั้งหมด
                            </a>
                            <a href="{{ route('admin.files') }}" class="btn btn-outline-info">
                                <i class="fas fa-folder me-2"></i>จัดการไฟล์
                            </a>
                            <a href="{{ route('admin.settings') }}" class="btn btn-outline-warning">
                                <i class="fas fa-cog me-2"></i>ตั้งค่าระบบ
                            </a>
                            <hr>
                            <a href="{{ route('news.index') }}" class="btn btn-outline-success" target="_blank">
                                <i class="fas fa-globe me-2"></i>ดูหน้าข่าวสาร
                            </a>
                            <a href="{{ route('home') }}" class="btn btn-outline-success" target="_blank">
                                <i class="fas fa-home me-2"></i>ดูหน้าหลัก
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Info -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>ข้อมูลระบบ
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2 text-sm">
                            <div class="col-6">
                                <strong>Laravel:</strong>
                            </div>
                            <div class="col-6">
                                {{ app()->version() }}
                            </div>
                            <div class="col-6">
                                <strong>PHP:</strong>
                            </div>
                            <div class="col-6">
                                {{ PHP_VERSION }}
                            </div>
                            <div class="col-6">
                                <strong>เซิร์ฟเวอร์:</strong>
                            </div>
                            <div class="col-6">
                                {{ $_SERVER['SERVER_SOFTWARE'] ?? 'N/A' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
@endpush
