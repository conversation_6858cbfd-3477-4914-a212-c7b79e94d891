<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

class AdminController extends Controller
{
    public function dashboard()
    {
        $stats = [
            'total_news' => News::count(),
            'published_news' => News::where('is_published', true)->count(),
            'draft_news' => News::where('is_published', false)->count(),
            'total_users' => User::count(),
        ];

        $recent_news = News::orderBy('created_at', 'desc')->limit(5)->get();
        $popular_news = News::orderBy('created_at', 'desc')->limit(5)->get(); // จะปรับเป็น view count ในอนาคต

        return view('admin.dashboard', compact('stats', 'recent_news', 'popular_news'));
    }

    public function newsIndex(Request $request)
    {
        $query = News::orderBy('created_at', 'desc');

        // ค้นหา
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // กรองตามหมวดหมู่
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // กรองตามสถานะ
        if ($request->filled('status')) {
            if ($request->status === 'published') {
                $query->where('is_published', true);
            } elseif ($request->status === 'draft') {
                $query->where('is_published', false);
            }
        }

        // เรียงลำดับ
        if ($request->filled('sort')) {
            switch ($request->sort) {
                case 'title':
                    $query->orderBy('title', 'asc');
                    break;
                case 'date_asc':
                    $query->orderBy('published_at', 'asc');
                    break;
                case 'date_desc':
                default:
                    $query->orderBy('published_at', 'desc');
                    break;
            }
        }

        $news = $query->paginate(15);
        $categories = News::distinct()->pluck('category')->filter();

        return view('admin.news.index', compact('news', 'categories'));
    }

    public function newsCreate()
    {
        return view('admin.news.create');
    }

    public function newsStore(Request $request)
    {
        $request->validate([
            'title' => 'required|max:255',
            'content' => 'required',
            'category' => 'required',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->all();

        // ตั้งค่าวันที่เผยแพร่
        if (!$data['published_at']) {
            $data['published_at'] = now();
        }

        // จัดการรูปภาพ
        if ($request->hasFile('image')) {
            $imageName = time().'.'.$request->image->extension();
            $request->image->move(public_path('images/news'), $imageName);
            $data['image'] = 'images/news/'.$imageName;
        }

        News::create($data);

        return redirect()->route('admin.news.index')->with('success', 'เพิ่มข่าวสารเรียบร้อยแล้ว');
    }

    public function newsEdit($id)
    {
        $news = News::findOrFail($id);
        return view('admin.news.edit', compact('news'));
    }

    public function newsUpdate(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|max:255',
            'content' => 'required',
            'category' => 'required',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $news = News::findOrFail($id);
        $data = $request->all();

        // จัดการรูปภาพ
        if ($request->hasFile('image')) {
            // ลบรูปเก่า
            if ($news->image && file_exists(public_path($news->image))) {
                unlink(public_path($news->image));
            }

            $imageName = time().'.'.$request->image->extension();
            $request->image->move(public_path('images/news'), $imageName);
            $data['image'] = 'images/news/'.$imageName;
        }

        $news->update($data);

        return redirect()->route('admin.news.index')->with('success', 'แก้ไขข่าวสารเรียบร้อยแล้ว');
    }

    public function newsDestroy($id)
    {
        $news = News::findOrFail($id);

        // ลบรูป
        if ($news->image && file_exists(public_path($news->image))) {
            unlink(public_path($news->image));
        }

        $news->delete();

        return redirect()->route('admin.news.index')->with('success', 'ลบข่าวสารเรียบร้อยแล้ว');
    }

    public function newsBulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,publish,unpublish',
            'selected_items' => 'required|array|min:1',
            'selected_items.*' => 'exists:news,id'
        ]);

        $action = $request->action;
        $selectedIds = $request->selected_items;

        switch ($action) {
            case 'delete':
                $news = News::whereIn('id', $selectedIds)->get();
                foreach ($news as $item) {
                    // ลบรูป
                    if ($item->image && file_exists(public_path($item->image))) {
                        unlink(public_path($item->image));
                    }
                }
                News::whereIn('id', $selectedIds)->delete();
                $message = 'ลบข่าวสารที่เลือกเรียบร้อยแล้ว';
                break;

            case 'publish':
                News::whereIn('id', $selectedIds)->update(['is_published' => true]);
                $message = 'เผยแพร่ข่าวสารที่เลือกเรียบร้อยแล้ว';
                break;

            case 'unpublish':
                News::whereIn('id', $selectedIds)->update(['is_published' => false]);
                $message = 'ยกเลิกการเผยแพร่ข่าวสารที่เลือกเรียบร้อยแล้ว';
                break;
        }

        return redirect()->route('admin.news.index')->with('success', $message);
    }

    public function settings()
    {
        $settings = [
            'site_name' => config('app.name', 'ระบบจัดการข่าวสาร'),
            'site_description' => 'ระบบจัดการข่าวสารและข้อมูล',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '02-xxx-xxxx',
            'contact_address' => 'ที่อยู่ติดต่อ',
        ];

        return view('admin.settings', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'site_name' => 'required|max:255',
            'site_description' => 'required|max:500',
            'contact_email' => 'required|email',
            'contact_phone' => 'required|max:20',
            'contact_address' => 'required|max:500',
        ]);

        // บันทึกการตั้งค่าลงไฟล์หรือฐานข้อมูล
        // สำหรับตอนนี้จะใช้ cache
        Cache::put('site_settings', $request->all(), now()->addDays(30));

        return redirect()->route('admin.settings')->with('success', 'บันทึกการตั้งค่าเรียบร้อยแล้ว');
    }

    public function fileManager()
    {
        $images = [];
        $imagePath = public_path('images');

        if (File::exists($imagePath)) {
            $files = File::allFiles($imagePath);
            foreach ($files as $file) {
                if (in_array(strtolower($file->getExtension()), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    $relativePath = 'images/' . $file->getRelativePathname();
                    $images[] = [
                        'name' => $file->getFilename(),
                        'path' => $relativePath,
                        'url' => asset($relativePath),
                        'size' => $file->getSize(),
                        'modified' => $file->getMTime(),
                    ];
                }
            }
        }

        return view('admin.files', compact('images'));
    }

    public function uploadFile(Request $request)
    {
        $request->validate([
            'file' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120' // 5MB
        ]);

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('images/uploads'), $filename);

            return response()->json([
                'success' => true,
                'message' => 'อัปโหลดไฟล์เรียบร้อยแล้ว',
                'file' => [
                    'name' => $filename,
                    'url' => asset('images/uploads/' . $filename)
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์'
        ]);
    }

    public function deleteFile($file)
    {
        $filePath = public_path('images/' . $file);

        if (File::exists($filePath)) {
            File::delete($filePath);
            return response()->json([
                'success' => true,
                'message' => 'ลบไฟล์เรียบร้อยแล้ว'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'ไม่พบไฟล์ที่ต้องการลบ'
        ]);
    }
}
