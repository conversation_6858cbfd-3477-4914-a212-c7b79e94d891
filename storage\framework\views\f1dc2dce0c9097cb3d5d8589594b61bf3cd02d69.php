<?php $__env->startSection('title', 'แดชบอร์ด - ระบบจัดการ'); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<section class="py-4 bg-gradient-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>แดชบอร์ด
                </h1>
                <p class="mb-0 opacity-75">ภาพรวมระบบจัดการข่าวสาร</p>
            </div>
            <div class="col-auto">
                <div class="text-end">
                    <small class="opacity-75">อัปเดตล่าสุด</small><br>
                    <strong><?php echo e(now()->format('d/m/Y H:i น.')); ?></strong>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Cards -->
<section class="py-4">
    <div class="container">
        <div class="row g-3 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-0">ข่าวทั้งหมด</h6>
                                <h2 class="mb-0"><?php echo e(number_format($stats['total_news'])); ?></h2>
                                <small class="opacity-75">รายการ</small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="fas fa-newspaper fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-black bg-opacity-25">
                        <a href="<?php echo e(route('admin.news.index')); ?>" class="text-white text-decoration-none">
                            <i class="fas fa-arrow-right me-1"></i>ดูทั้งหมด
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-0">เผยแพร่แล้ว</h6>
                                <h2 class="mb-0"><?php echo e(number_format($stats['published_news'])); ?></h2>
                                <small class="opacity-75">รายการ</small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="fas fa-eye fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-black bg-opacity-25">
                        <small>
                            <?php echo e($stats['total_news'] > 0 ? round(($stats['published_news'] / $stats['total_news']) * 100, 1) : 0); ?>% ของทั้งหมด
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-0">ข่าวเด่น</h6>
                                <h2 class="mb-0"><?php echo e(number_format($stats['featured_news'])); ?></h2>
                                <small class="opacity-75">รายการ</small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="fas fa-star fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-black bg-opacity-25">
                        <small>แสดงในหน้าหลัก</small>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="card bg-info text-white h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-0">วันนี้</h6>
                                <h2 class="mb-0"><?php echo e(number_format($stats['today_news'])); ?></h2>
                                <small class="opacity-75">รายการใหม่</small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="fas fa-calendar-day fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-black bg-opacity-25">
                        <small><?php echo e(now()->format('d M Y')); ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Stats -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-file-alt fa-2x text-secondary mb-2"></i>
                        <h5 class="card-title"><?php echo e(number_format($stats['draft_news'])); ?></h5>
                        <p class="card-text text-muted">ร่าง</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-images fa-2x text-secondary mb-2"></i>
                        <h5 class="card-title"><?php echo e(number_format($stats['total_images'])); ?></h5>
                        <p class="card-text text-muted">รูปภาพ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-calendar-week fa-2x text-secondary mb-2"></i>
                        <h5 class="card-title"><?php echo e(number_format($stats['this_week_news'])); ?></h5>
                        <p class="card-text text-muted">สัปดาห์นี้</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-calendar-alt fa-2x text-secondary mb-2"></i>
                        <h5 class="card-title"><?php echo e(number_format($stats['this_month_news'])); ?></h5>
                        <p class="card-text text-muted">เดือนนี้</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <!-- Recent News -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i>ข่าวล่าสุด
                        </h5>
                        <a href="<?php echo e(route('admin.news.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>เพิ่มข่าวใหม่
                        </a>
                    </div>
                    <div class="card-body p-0">
                        <?php if($recent_news->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>หัวข้อ</th>
                                        <th>หมวดหมู่</th>
                                        <th>สถานะ</th>
                                        <th>วันที่</th>
                                        <th>จัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recent_news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($news->display_image): ?>
                                                    <img src="<?php echo e($news->display_image); ?>" class="rounded me-2" 
                                                         width="32" height="32" style="object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                                         style="width: 32px; height: 32px;">
                                                        <i class="fas fa-newspaper text-muted small"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <h6 class="mb-0"><?php echo e(Str::limit($news->title, 40)); ?></h6>
                                                    <?php if($news->is_featured): ?>
                                                        <small class="text-warning">
                                                            <i class="fas fa-star me-1"></i>เด่น
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e($news->category); ?></span>
                                        </td>
                                        <td>
                                            <?php if($news->is_published): ?>
                                                <span class="badge bg-success">เผยแพร่</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">ร่าง</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo e($news->created_at->format('d/m/Y')); ?>

                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo e(route('admin.news.edit', $news->id)); ?>" 
                                                   class="btn btn-outline-warning" title="แก้ไข">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="<?php echo e(route('news.show', $news->id)); ?>" 
                                                   class="btn btn-outline-info" title="ดู" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">ยังไม่มีข่าวสาร</h6>
                            <a href="<?php echo e(route('admin.news.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เพิ่มข่าวแรก
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php if($recent_news->count() > 0): ?>
                    <div class="card-footer bg-light text-center">
                        <a href="<?php echo e(route('admin.news.index')); ?>" class="text-decoration-none">
                            ดูข่าวทั้งหมด <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Popular Categories -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie me-2"></i>หมวดหมู่ยอดนิยม
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if($popular_categories->count() > 0): ?>
                        <?php $__currentLoopData = $popular_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-0"><?php echo e($category->category); ?></h6>
                                <small class="text-muted"><?php echo e($category->count); ?> ข่าว</small>
                            </div>
                            <div class="progress" style="width: 100px; height: 8px;">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: <?php echo e(($category->count / $stats['published_news']) * 100); ?>%">
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-tags fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">ยังไม่มีข้อมูลหมวดหมู่</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-4">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>การดำเนินการด่วน
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('admin.news.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เพิ่มข่าวใหม่
                            </a>
                            <a href="<?php echo e(route('admin.news.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-list me-2"></i>จัดการข่าวทั้งหมด
                            </a>
                            <a href="<?php echo e(route('news.index')); ?>" class="btn btn-outline-info" target="_blank">
                                <i class="fas fa-globe me-2"></i>ดูหน้าข่าวสาร
                            </a>
                            <a href="<?php echo e(route('home')); ?>" class="btn btn-outline-success" target="_blank">
                                <i class="fas fa-home me-2"></i>ดูหน้าหลัก
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
    }
    
    .card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .progress {
        background-color: #e9ecef;
    }
    
    .progress-bar {
        background: linear-gradient(45deg, #007bff, #0056b3);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Auto refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
    
    // Add tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\shop\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>