@extends('layouts.app')

@section('title', 'จัดการไฟล์ - Admin')

@section('content')
<!-- <PERSON> Header -->
<section class="py-4 bg-info text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-folder me-2"></i>จัดการไฟล์
                </h1>
                <p class="mb-0 opacity-75">อัปโหลดและจัดการรูปภาพ</p>
            </div>
            <div class="col-auto">
                <div class="btn-group">
                    <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fas fa-upload me-2"></i>อัปโหลดไฟล์
                    </button>
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left me-2"></i>กลับแดชบอร์ด
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- File Manager -->
<section class="py-4">
    <div class="container">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        <!-- Upload Area -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-1">
                            <i class="fas fa-cloud-upload-alt me-2"></i>อัปโหลดไฟล์
                        </h5>
                        <p class="text-muted mb-0">ลากไฟล์มาวางหรือคลิกเพื่อเลือกไฟล์</p>
                    </div>
                    <div class="col-auto">
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-plus me-2"></i>เลือกไฟล์
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Files Grid -->
        @if(count($images) > 0)
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-images me-2"></i>รูปภาพทั้งหมด ({{ count($images) }} ไฟล์)
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-secondary active" onclick="setView('grid')">
                                <i class="fas fa-th"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setView('list')">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="gridView" class="row g-3">
                        @foreach($images as $image)
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <div class="card border">
                                <div class="position-relative">
                                    <img src="{{ $image['url'] }}" 
                                         class="card-img-top" 
                                         style="height: 150px; object-fit: cover;"
                                         alt="{{ $image['name'] }}">
                                    <div class="position-absolute top-0 end-0 p-1">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-dark btn-sm" type="button" 
                                                    data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ $image['url'] }}" target="_blank">
                                                        <i class="fas fa-eye me-2"></i>ดู
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="copyUrl('{{ $image['url'] }}')">
                                                        <i class="fas fa-copy me-2"></i>คัดลอก URL
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" 
                                                       onclick="deleteFile('{{ $image['name'] }}')">
                                                        <i class="fas fa-trash me-2"></i>ลบ
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-2">
                                    <h6 class="card-title mb-1 small">{{ Str::limit($image['name'], 20) }}</h6>
                                    <p class="card-text small text-muted mb-0">
                                        {{ number_format($image['size'] / 1024, 1) }} KB
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <div id="listView" class="d-none">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>รูปภาพ</th>
                                        <th>ชื่อไฟล์</th>
                                        <th>ขนาด</th>
                                        <th>วันที่แก้ไข</th>
                                        <th width="120">จัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($images as $image)
                                    <tr>
                                        <td>
                                            <img src="{{ $image['url'] }}" 
                                                 class="rounded" 
                                                 style="width: 50px; height: 50px; object-fit: cover;"
                                                 alt="{{ $image['name'] }}">
                                        </td>
                                        <td>
                                            <strong>{{ $image['name'] }}</strong><br>
                                            <small class="text-muted">{{ $image['path'] }}</small>
                                        </td>
                                        <td>{{ number_format($image['size'] / 1024, 1) }} KB</td>
                                        <td>{{ date('d/m/Y H:i', $image['modified']) }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ $image['url'] }}" target="_blank" 
                                                   class="btn btn-outline-primary" title="ดู">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-info" 
                                                        onclick="copyUrl('{{ $image['url'] }}')" title="คัดลอก URL">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="deleteFile('{{ $image['name'] }}')" title="ลบ">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">ยังไม่มีไฟล์</h5>
                    <p class="text-muted">อัปโหลดไฟล์เพื่อเริ่มต้นใช้งาน</p>
                    <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fas fa-upload me-2"></i>อัปโหลดไฟล์แรก
                    </button>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>อัปโหลดไฟล์
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label for="file" class="form-label">เลือกไฟล์รูปภาพ</label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept="image/*" required>
                        <div class="form-text">
                            รองรับไฟล์: JPG, PNG, GIF, WebP ขนาดไม่เกิน 5MB
                        </div>
                    </div>
                    <div id="preview" class="mb-3 d-none">
                        <label class="form-label">ตัวอย่าง</label>
                        <div>
                            <img id="previewImg" src="" class="img-thumbnail" style="max-width: 200px;">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-info" onclick="uploadFile()">
                    <i class="fas fa-upload me-2"></i>อัปโหลด
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// File preview
document.getElementById('file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImg').src = e.target.result;
            document.getElementById('preview').classList.remove('d-none');
        };
        reader.readAsDataURL(file);
    }
});

// Upload file
function uploadFile() {
    const form = document.getElementById('uploadForm');
    const formData = new FormData(form);
    
    fetch('{{ route("admin.files.upload") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
        }
    })
    .catch(error => {
        alert('เกิดข้อผิดพลาดในการอัปโหลด');
    });
}

// Delete file
function deleteFile(filename) {
    if (confirm('คุณต้องการลบไฟล์ ' + filename + ' ใช่หรือไม่?')) {
        fetch('/admin/files/' + encodeURIComponent(filename), {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        });
    }
}

// Copy URL
function copyUrl(url) {
    navigator.clipboard.writeText(url).then(function() {
        alert('คัดลอก URL เรียบร้อยแล้ว');
    });
}

// View toggle
function setView(view) {
    if (view === 'grid') {
        document.getElementById('gridView').classList.remove('d-none');
        document.getElementById('listView').classList.add('d-none');
    } else {
        document.getElementById('gridView').classList.add('d-none');
        document.getElementById('listView').classList.remove('d-none');
    }
}
</script>
@endpush
