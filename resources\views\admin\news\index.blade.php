@extends('layouts.app')

@section('title', 'จัดการข่าวสาร')

@section('content')
<!-- Page Header -->
<section class="py-4 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-newspaper me-2"></i>จัดการข่าวสาร
                </h1>
                <p class="mb-0 opacity-75">เพิ่ม แก้ไข และลบข่าวสาร</p>
            </div>
            <div class="col-auto">
                <a href="{{ route('admin.news.create') }}" class="btn btn-light">
                    <i class="fas fa-plus me-2"></i>เพิ่มข่าวใหม่
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Success Message -->
@if(session('success'))
<div class="container mt-3">
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>
@endif

<!-- News Management -->
<section class="py-4">
    <div class="container">
        <!-- Search -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.news.index') }}" class="row g-3 align-items-end">
                    <div class="col-md-8">
                        <label for="search" class="form-label">ค้นหาข่าว</label>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="ค้นหาจากหัวข้อหรือเนื้อหา..."
                               value="{{ request('search') }}">
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>ค้นหา
                            </button>
                            <a href="{{ route('admin.news.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-1"></i>รีเซ็ต
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- News Table -->
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">รายการข่าวสาร ({{ $news->total() }} รายการ)</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{{ route('admin.news.create') }}" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>เพิ่มข่าวใหม่
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card-body p-0">
                @if(isset($news) && $news->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">#</th>
                                <th width="45%">หัวข้อ</th>
                                <th width="15%">หมวดหมู่</th>
                                <th width="10%">สถานะ</th>
                                <th width="15%">วันที่สร้าง</th>
                                <th width="10%">จัดการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($news as $item)
                            <tr>
                                <td>{{ $item->id }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($item->display_image)
                                            <img src="{{ $item->display_image }}" class="rounded me-2"
                                                 width="40" height="40" style="object-fit: cover;">
                                        @else
                                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center"
                                                 style="width: 40px; height: 40px;">
                                                <i class="fas fa-newspaper text-muted"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <h6 class="mb-0">{{ Str::limit($item->title, 50) }}</h6>
                                            @if($item->is_featured)
                                                <small class="text-warning">
                                                    <i class="fas fa-star me-1"></i>ข่าวเด่น
                                                </small>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $item->category }}</span>
                                </td>
                                <td>
                                    @if($item->is_published)
                                        <span class="badge bg-success">เผยแพร่</span>
                                    @else
                                        <span class="badge bg-secondary">ร่าง</span>
                                    @endif
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ $item->created_at->format('d/m/Y H:i') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ route('news.show', $item->id) }}"
                                           class="btn btn-outline-info" title="ดู" target="_blank">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.news.edit', $item->id) }}"
                                           class="btn btn-outline-warning" title="แก้ไข">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="deleteNews({{ $item->id }})" title="ลบ">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                @if($news->hasPages())
                <div class="card-footer bg-white">
                    <div class="d-flex justify-content-center">
                        {{ $news->links() }}
                    </div>
                </div>
                @endif
                
                @else
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-5x text-muted mb-3"></i>
                    <h5 class="text-muted">ยังไม่มีข่าวสาร</h5>
                    <p class="text-muted">เริ่มต้นสร้างข่าวสารแรกของคุณ</p>
                    <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มข่าวใหม่
                    </a>
                </div>
                @endif
            </div>
        </div>


    </div>
</section>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ยืนยันการลบ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>คุณแน่ใจหรือไม่ที่จะลบข่าวสารนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">ลบ</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function deleteNews(id) {
        if (confirm('คุณแน่ใจหรือไม่ที่จะลบข่าวนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้')) {
            const form = document.getElementById('deleteForm');
            form.action = `/admin/news/${id}`;
            form.submit();
        }
    }

    // Auto-hide alerts
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
</script>
@endpush
