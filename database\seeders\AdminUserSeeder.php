<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // สร้างผู้ใช้ admin
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );

        // สร้างผู้ใช้ editor
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Editor',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'editor',
                'email_verified_at' => now(),
            ]
        );

        echo "Admin users created:\n";
        echo "Admin: <EMAIL> / password\n";
        echo "Editor: <EMAIL> / password\n";
    }
}
