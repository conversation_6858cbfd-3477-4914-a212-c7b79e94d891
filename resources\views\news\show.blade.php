@extends('layouts.app')

@section('title', $news->title . ' - บริการรับจัดงานศพแบบครบวงจร')

@section('content')
<!-- Breadcrumb -->
<section class="py-3 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item">
                    <a href="{{ route('home') }}" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>หน้าหลัก
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ route('news.index') }}" class="text-decoration-none">ข่าวสาร</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">{{ $news->title }}</li>
            </ol>
        </nav>
    </div>
</section>

<!-- News Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <article class="card shadow-sm">
                    <!-- News Header -->
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex flex-wrap align-items-center gap-2 mb-3">
                            <span class="badge bg-primary">{{ $news->category }}</span>
                            @if($news->is_featured)
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>ข่าวเด่น
                                </span>
                            @endif
                        </div>
                        
                        <h1 class="card-title h2 mb-3">{{ $news->title }}</h1>
                        
                        <div class="d-flex flex-wrap align-items-center text-muted small gap-3">
                            <span>
                                <i class="fas fa-calendar me-1"></i>
                                {{ $news->formatted_date }}
                            </span>
                            <span>
                                <i class="fas fa-clock me-1"></i>
                                {{ $news->created_at->format('H:i น.') }}
                            </span>
                        </div>
                    </div>
                    
                    <!-- News Image -->
                    @if($news->image)
                    <div class="position-relative">
                        <img src="{{ asset($news->image) }}" class="card-img-top"
                             alt="{{ $news->title }}" style="height: 400px; object-fit: cover;">
                    </div>
                    @endif
                    
                    <!-- News Body -->
                    <div class="card-body">
                        <div class="news-content">
                            {!! nl2br(e($news->content)) !!}
                        </div>
                        
                        <!-- Share Buttons -->
                        <div class="border-top pt-4 mt-4">
                            <h6 class="mb-3">แชร์ข่าวนี้:</h6>
                            <div class="d-flex gap-2">
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-facebook-f me-1"></i>Facebook
                                </a>
                                <a href="#" class="btn btn-outline-info btn-sm">
                                    <i class="fab fa-twitter me-1"></i>Twitter
                                </a>
                                <a href="#" class="btn btn-outline-success btn-sm">
                                    <i class="fab fa-line me-1"></i>Line
                                </a>
                                <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                                    <i class="fas fa-copy me-1"></i>คัดลอกลิงก์
                                </button>
                            </div>
                        </div>
                    </div>
                </article>
                
                <!-- Navigation -->
                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('news.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>กลับไปหน้าข่าวสาร
                    </a>
                    
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>พิมพ์
                        </button>
                        <button class="btn btn-outline-secondary" onclick="window.history.back()">
                            <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Related News -->
                @if(isset($relatedNews) && $relatedNews->count() > 0)
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-newspaper me-2"></i>ข่าวที่เกี่ยวข้อง
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        @foreach($relatedNews as $related)
                        <div class="border-bottom p-3">
                            <div class="row g-2">
                                @if($related->display_image)
                                <div class="col-4">
                                    <img src="{{ $related->display_image }}" class="img-fluid rounded"
                                         alt="{{ $related->title }}" style="height: 60px; object-fit: cover;">
                                </div>
                                <div class="col-8">
                                @else
                                <div class="col-12">
                                @endif
                                    <h6 class="mb-1">
                                        <a href="{{ route('news.show', $related->id) }}" 
                                           class="text-decoration-none text-dark">
                                            {{ Str::limit($related->title, 50) }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ $related->formatted_date }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <!-- Contact Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-phone me-2"></i>ติดต่อเรา
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">ต้องการคำปรึกษาหรือสอบถามข้อมูลเพิ่มเติม?</p>
                        
                        <div class="d-grid gap-2">
                            <a href="tel:02-123-4567" class="btn btn-outline-success">
                                <i class="fas fa-phone me-2"></i>02-123-4567
                            </a>
                            <a href="tel:08-1234-5678" class="btn btn-outline-success">
                                <i class="fas fa-mobile-alt me-2"></i>08-1234-5678
                            </a>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                                <i class="fas fa-envelope me-2"></i>ส่งอีเมล
                            </a>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                บริการตลอด 24 ชั่วโมง
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Services Card -->
                <div class="card shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-hands-helping me-2"></i>บริการของเรา
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                จัดงานศพแบบไทย
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                จัดงานศพแบบจีน
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                บริการฌาปนกิจ
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                บริการรถรับส่ง
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success me-2"></i>
                                ถ่ายทอดสดพิธีศพ
                            </li>
                        </ul>
                        
                        <div class="text-center mt-3">
                            <a href="{{ route('home') }}#services" class="btn btn-info btn-sm">
                                ดูบริการทั้งหมด
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
    .news-content {
        font-size: 1.1rem;
        line-height: 1.8;
        color: #333;
    }
    
    .news-content p {
        margin-bottom: 1.5rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        font-weight: bold;
    }
    
    @media print {
        .btn, .card-header, nav, footer {
            display: none !important;
        }
        
        .card {
            border: none !important;
            box-shadow: none !important;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    function copyToClipboard() {
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(function() {
            // Show success message
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check me-1"></i>คัดลอกแล้ว!';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-success');

            setTimeout(function() {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-secondary');
            }, 2000);
        });
    }



    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
</script>
@endpush
