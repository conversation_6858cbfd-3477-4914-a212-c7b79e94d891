<?php $__env->startSection('title', 'แก้ไขข่าว: ' . $news->title . ' - Admin'); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<section class="py-4 bg-warning text-dark">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-edit me-2"></i>แก้ไขข่าว
                </h1>
                <p class="mb-0 opacity-75">แก้ไขข้อมูลข่าวสาร: <?php echo e($news->title); ?></p>
            </div>
            <div class="col-auto">
                <div class="btn-group">
                    <a href="<?php echo e(route('news.show', $news->id)); ?>" class="btn btn-outline-dark" target="_blank">
                        <i class="fas fa-eye me-2"></i>ดูข่าว
                    </a>
                    <a href="<?php echo e(route('admin.news.index')); ?>" class="btn btn-dark">
                        <i class="fas fa-arrow-left me-2"></i>กลับไปจัดการข่าว
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Form Section -->
<section class="py-4">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>แก้ไขข้อมูลข่าวสาร
                        </h5>
                    </div>
                    
                    <div class="card-body">
                        <?php if($errors->any()): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>กรุณาแก้ไขข้อผิดพลาดต่อไปนี้:</h6>
                            <ul class="mb-0">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                        
                        <form action="<?php echo e(route('admin.news.update', $news->id)); ?>" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            
                            <!-- Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">
                                    หัวข้อข่าว <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="title" name="title" value="<?php echo e(old('title', $news->title)); ?>" 
                                       placeholder="กรอกหัวข้อข่าวสาร" required>
                                <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <!-- Category -->
                            <div class="mb-3">
                                <label for="category" class="form-label">
                                    หมวดหมู่ <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="category" name="category" required>
                                    <option value="">เลือกหมวดหมู่</option>
                                    <option value="ข่าวทั่วไป" <?php echo e(old('category', $news->category) == 'ข่าวทั่วไป' ? 'selected' : ''); ?>>ข่าวทั่วไป</option>
                                    <option value="บริการใหม่" <?php echo e(old('category', $news->category) == 'บริการใหม่' ? 'selected' : ''); ?>>บริการใหม่</option>
                                    <option value="แนวทางปฏิบัติ" <?php echo e(old('category', $news->category) == 'แนวทางปฏิบัติ' ? 'selected' : ''); ?>>แนวทางปฏิบัติ</option>
                                    <option value="ประเพณีไทย" <?php echo e(old('category', $news->category) == 'ประเพณีไทย' ? 'selected' : ''); ?>>ประเพณีไทย</option>
                                    <option value="พิธีกรรม" <?php echo e(old('category', $news->category) == 'พิธีกรรม' ? 'selected' : ''); ?>>พิธีกรรม</option>
                                    <option value="คำแนะนำ" <?php echo e(old('category', $news->category) == 'คำแนะนำ' ? 'selected' : ''); ?>>คำแนะนำ</option>
                                    <option value="สุขภาพจิต" <?php echo e(old('category', $news->category) == 'สุขภาพจิต' ? 'selected' : ''); ?>>สุขภาพจิต</option>
                                </select>
                                <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <!-- Excerpt -->
                            <div class="mb-3">
                                <label for="excerpt" class="form-label">สรุปข่าว</label>
                                <textarea class="form-control <?php $__errorArgs = ['excerpt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="excerpt" name="excerpt" rows="3" 
                                          placeholder="สรุปสั้นๆ ของข่าวสาร (ถ้าไม่กรอกจะใช้ข้อความต้นของเนื้อหา)"><?php echo e(old('excerpt', $news->excerpt)); ?></textarea>
                                <?php $__errorArgs = ['excerpt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <!-- Content -->
                            <div class="mb-3">
                                <label for="content" class="form-label">
                                    เนื้อหาข่าว <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="content" name="content" rows="10" 
                                          placeholder="เขียนเนื้อหาข่าวสารที่ต้องการแจ้งให้ทราบ" required><?php echo e(old('content', $news->content)); ?></textarea>
                                <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <!-- Current Image -->
                            <?php if($news->image): ?>
                            <div class="mb-3">
                                <label class="form-label">รูปภาพปัจจุบัน</label>
                                <div class="d-flex align-items-center gap-3">
                                    <img src="<?php echo e(asset($news->image)); ?>" class="img-thumbnail" style="max-width: 200px;">
                                    <div>
                                        <p class="mb-1"><strong><?php echo e(basename($news->image)); ?></strong></p>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
                                            <label class="form-check-label text-danger" for="remove_image">
                                                <i class="fas fa-trash me-1"></i>ลบรูปภาพนี้
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <!-- New Image -->
                            <div class="mb-3">
                                <label for="image" class="form-label">
                                    <?php echo e($news->image ? 'เปลี่ยนรูปภาพหลักใหม่' : 'รูปภาพหลัก'); ?>

                                </label>
                                <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="image" name="image" accept="image/*">
                                <div class="form-text">
                                    รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB (แบบเก่า)
                                </div>
                                <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Image Preview -->
                            <div class="mb-3" id="imagePreview" style="display: none;">
                                <label class="form-label">ตัวอย่างรูปภาพใหม่</label>
                                <div>
                                    <img id="previewImg" src="" class="img-thumbnail" style="max-width: 300px;">
                                </div>
                            </div>

                            <!-- Current Gallery Images -->
                            <?php if($news->images->count() > 0): ?>
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-images me-2"></i>อัลบั้มรูปภาพปัจจุบัน
                                </label>
                                <div class="row g-2" id="currentGallery">
                                    <?php $__currentLoopData = $news->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-3 col-sm-4 col-6" id="image-<?php echo e($image->id); ?>">
                                        <div class="card">
                                            <img src="<?php echo e($image->image_url); ?>" class="card-img-top"
                                                 style="height: 120px; object-fit: cover;">
                                            <div class="card-body p-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <?php if($image->is_featured): ?>
                                                            <i class="fas fa-star text-warning me-1"></i>รูปหลัก
                                                        <?php else: ?>
                                                            รูปที่ <?php echo e($image->sort_order + 1); ?>

                                                        <?php endif; ?>
                                                    </small>
                                                    <div class="btn-group btn-group-sm">
                                                        <?php if(!$image->is_featured): ?>
                                                        <button type="button" class="btn btn-outline-warning btn-sm"
                                                                onclick="setFeatured(<?php echo e($news->id); ?>, <?php echo e($image->id); ?>)"
                                                                title="ตั้งเป็นรูปหลัก">
                                                            <i class="fas fa-star"></i>
                                                        </button>
                                                        <?php endif; ?>
                                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                                onclick="deleteImage(<?php echo e($news->id); ?>, <?php echo e($image->id); ?>)"
                                                                title="ลบรูป">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Add New Gallery Images -->
                            <div class="mb-3">
                                <label for="gallery_images" class="form-label">
                                    <i class="fas fa-plus me-2"></i>เพิ่มรูปภาพใหม่ในอัลบั้ม
                                </label>
                                <input type="file" class="form-control <?php $__errorArgs = ['gallery_images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    สามารถเลือกหลายรูปพร้อมกัน รูปใหม่จะเพิ่มเข้าไปในอัลบั้ม<br>
                                    รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB ต่อรูป
                                </div>
                                <?php $__errorArgs = ['gallery_images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- New Gallery Preview -->
                            <div class="mb-3" id="galleryPreview" style="display: none;">
                                <label class="form-label">ตัวอย่างรูปภาพใหม่ที่จะเพิ่ม</label>
                                <div class="row g-2" id="galleryContainer">
                                    <!-- รูปภาพจะแสดงที่นี่ -->
                                </div>
                            </div>
                            
                            <!-- Options -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="is_featured" name="is_featured" value="1"
                                               <?php echo e(old('is_featured', $news->is_featured) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_featured">
                                            <i class="fas fa-star text-warning me-1"></i>
                                            ข่าวเด่น (แสดงในหน้าแรก)
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="is_published" name="is_published" value="1"
                                               <?php echo e(old('is_published', $news->is_published) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_published">
                                            <i class="fas fa-eye text-success me-1"></i>
                                            เผยแพร่
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Published Date -->
                            <div class="mb-4">
                                <label for="published_at" class="form-label">วันที่เผยแพร่</label>
                                <div class="input-group">
                                    <input type="datetime-local" class="form-control <?php $__errorArgs = ['published_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="published_at" name="published_at"
                                           value="<?php echo e(old('published_at', $news->published_at ? $news->published_at->format('Y-m-d\TH:i') : now()->format('Y-m-d\TH:i'))); ?>">
                                    <button type="button" class="btn btn-outline-secondary" id="setCurrentDateTime">
                                        <i class="fas fa-clock me-1"></i>ตอนนี้
                                    </button>
                                </div>
                                <?php $__errorArgs = ['published_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <!-- Submit Buttons -->
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                                </button>
                                <a href="<?php echo e(route('admin.news.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>ยกเลิก
                                </a>
                                <a href="<?php echo e(route('news.show', $news->id)); ?>" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-eye me-2"></i>ดูข่าว
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Info Card -->
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>ข้อมูลข่าว
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>สร้างเมื่อ:</strong> <?php echo e($news->created_at->format('d/m/Y H:i น.')); ?></p>
                                <p><strong>แก้ไขล่าสุด:</strong> <?php echo e($news->updated_at->format('d/m/Y H:i น.')); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>สถานะ:</strong> 
                                    <?php if($news->is_published): ?>
                                        <span class="badge bg-success">เผยแพร่แล้ว</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">ร่าง</span>
                                    <?php endif; ?>
                                </p>
                                <p><strong>ข่าวเด่น:</strong> 
                                    <?php if($news->is_featured): ?>
                                        <span class="badge bg-warning text-dark">ใช่</span>
                                    <?php else: ?>
                                        <span class="badge bg-light text-dark">ไม่ใช่</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    });

    // Gallery images preview
    document.getElementById('gallery_images').addEventListener('change', function(e) {
        const files = e.target.files;
        const preview = document.getElementById('galleryPreview');
        const container = document.getElementById('galleryContainer');

        container.innerHTML = '';

        if (files.length > 0) {
            preview.style.display = 'block';

            Array.from(files).forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const col = document.createElement('div');
                    col.className = 'col-md-3 col-sm-4 col-6';

                    col.innerHTML = `
                        <div class="card">
                            <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">
                            <div class="card-body p-2">
                                <small class="text-muted">รูปใหม่ที่ ${index + 1}</small>
                            </div>
                        </div>
                    `;

                    container.appendChild(col);
                };
                reader.readAsDataURL(file);
            });
        } else {
            preview.style.display = 'none';
        }
    });

    // Set current date time
    document.getElementById('setCurrentDateTime').addEventListener('click', function() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        const dateTimeString = `${year}-${month}-${day}T${hours}:${minutes}`;
        document.getElementById('published_at').value = dateTimeString;
    });

    // Delete image function
    function deleteImage(newsId, imageId) {
        if (confirm('คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?')) {
            fetch(`/admin/news/${newsId}/images/${imageId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById(`image-${imageId}`).remove();

                    // Show success message
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success alert-dismissible fade show';
                    alert.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>ลบรูปภาพเรียบร้อยแล้ว
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.querySelector('.container').insertBefore(alert, document.querySelector('.card'));

                    // Auto hide after 3 seconds
                    setTimeout(() => {
                        alert.remove();
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการลบรูปภาพ');
            });
        }
    }

    // Set featured image function
    function setFeatured(newsId, imageId) {
        fetch(`/admin/news/${newsId}/images/${imageId}/featured`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to update the UI
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการตั้งรูปหลัก');
        });
    }

    // Auto-resize textarea
    document.getElementById('content').addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Character counter for title
    const titleInput = document.getElementById('title');
    const titleLabel = titleInput.previousElementSibling;

    titleInput.addEventListener('input', function() {
        const length = this.value.length;
        const maxLength = 255;
        const remaining = maxLength - length;

        if (remaining < 50) {
            titleLabel.innerHTML = `หัวข้อข่าว <span class="text-danger">*</span> <small class="text-muted">(เหลือ ${remaining} ตัวอักษร)</small>`;
        } else {
            titleLabel.innerHTML = `หัวข้อข่าว <span class="text-danger">*</span>`;
        }
    });

    // Remove image checkbox
    document.getElementById('remove_image')?.addEventListener('change', function() {
        const imageInput = document.getElementById('image');
        if (this.checked) {
            imageInput.required = false;
            imageInput.parentElement.querySelector('label').textContent = 'รูปภาพใหม่ (จำเป็น เนื่องจากจะลบรูปเก่า)';
        } else {
            imageInput.required = false;
            imageInput.parentElement.querySelector('label').textContent = 'เปลี่ยนรูปภาพหลักใหม่';
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\shop\resources\views/admin/news/edit.blade.php ENDPATH**/ ?>