@extends('layouts.app')

@section('title', 'ตั้งค่าระบบ - Admin')

@section('content')
<!-- <PERSON> Header -->
<section class="py-4 bg-warning text-dark">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-cog me-2"></i>ตั้งค่าระบบ
                </h1>
                <p class="mb-0 opacity-75">จัดการการตั้งค่าเว็บไซต์และข้อมูลติดต่อ</p>
            </div>
            <div class="col-auto">
                <a href="{{ route('admin.dashboard') }}" class="btn btn-dark">
                    <i class="fas fa-arrow-left me-2"></i>กลับแดชบอร์ด
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Settings Form -->
<section class="py-4">
    <div class="container">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-sliders-h me-2"></i>การตั้งค่าทั่วไป
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.settings.update') }}" method="POST">
                            @csrf
                            
                            <!-- Site Information -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-globe me-2"></i>ข้อมูลเว็บไซต์
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="site_name" class="form-label">
                                        ชื่อเว็บไซต์ <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control @error('site_name') is-invalid @enderror" 
                                           id="site_name" name="site_name" 
                                           value="{{ old('site_name', $settings['site_name']) }}" 
                                           placeholder="ชื่อเว็บไซต์" required>
                                    @error('site_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="site_description" class="form-label">
                                        คำอธิบายเว็บไซต์ <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control @error('site_description') is-invalid @enderror" 
                                              id="site_description" name="site_description" rows="3"
                                              placeholder="คำอธิบายเว็บไซต์สำหรับ SEO" required>{{ old('site_description', $settings['site_description']) }}</textarea>
                                    @error('site_description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-address-book me-2"></i>ข้อมูลติดต่อ
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">
                                        อีเมลติดต่อ <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" class="form-control @error('contact_email') is-invalid @enderror" 
                                           id="contact_email" name="contact_email" 
                                           value="{{ old('contact_email', $settings['contact_email']) }}" 
                                           placeholder="<EMAIL>" required>
                                    @error('contact_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">
                                        เบอร์โทรติดต่อ <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control @error('contact_phone') is-invalid @enderror" 
                                           id="contact_phone" name="contact_phone" 
                                           value="{{ old('contact_phone', $settings['contact_phone']) }}" 
                                           placeholder="02-xxx-xxxx" required>
                                    @error('contact_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="contact_address" class="form-label">
                                        ที่อยู่ติดต่อ <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control @error('contact_address') is-invalid @enderror" 
                                              id="contact_address" name="contact_address" rows="3"
                                              placeholder="ที่อยู่สำหรับติดต่อ" required>{{ old('contact_address', $settings['contact_address']) }}</textarea>
                                    @error('contact_address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>บันทึกการตั้งค่า
                                </button>
                                <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>ยกเลิก
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Additional Settings -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tools me-2"></i>เครื่องมือเพิ่มเติม
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-grid">
                                    <button type="button" class="btn btn-outline-info" onclick="clearCache()">
                                        <i class="fas fa-broom me-2"></i>ล้าง Cache
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid">
                                    <a href="{{ route('admin.files') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-folder me-2"></i>จัดการไฟล์
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>ข้อมูลระบบ
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-sm-4"><strong>Laravel Version:</strong></div>
                            <div class="col-sm-8">{{ app()->version() }}</div>
                            
                            <div class="col-sm-4"><strong>PHP Version:</strong></div>
                            <div class="col-sm-8">{{ PHP_VERSION }}</div>
                            
                            <div class="col-sm-4"><strong>Server:</strong></div>
                            <div class="col-sm-8">{{ $_SERVER['SERVER_SOFTWARE'] ?? 'N/A' }}</div>
                            
                            <div class="col-sm-4"><strong>Database:</strong></div>
                            <div class="col-sm-8">{{ config('database.default') }}</div>
                            
                            <div class="col-sm-4"><strong>Environment:</strong></div>
                            <div class="col-sm-8">
                                <span class="badge {{ app()->environment('production') ? 'bg-success' : 'bg-warning' }}">
                                    {{ app()->environment() }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
function clearCache() {
    if (confirm('คุณต้องการล้าง Cache ใช่หรือไม่?')) {
        // ส่ง AJAX request ไปล้าง cache
        fetch('/admin/clear-cache', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('ล้าง Cache เรียบร้อยแล้ว');
            } else {
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        })
        .catch(error => {
            alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
        });
    }
}
</script>
@endpush
