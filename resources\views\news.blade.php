<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>ข่าวสารประชาสัมพันธ์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #f8fafc; }
        .news-card { margin-bottom: 1.5rem; }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="mb-4 text-center">ข่าวสารประชาสัมพันธ์</h1>
        @foreach($news as $item)
            <div class="card news-card shadow-sm">
                <div class="card-body">
                    <h4 class="card-title">{{ $item['title'] }}</h4>
                    <h6 class="card-subtitle mb-2 text-muted">{{ \Carbon\Carbon::parse($item['date'])->format('d/m/Y') }}</h6>
                    <p class="card-text">{{ $item['content'] }}</p>
                </div>
            </div>
        @endforeach
    </div>
</body>
</html> 